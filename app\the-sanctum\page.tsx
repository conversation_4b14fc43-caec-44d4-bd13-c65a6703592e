"use client"

import SharedLayout from "@/components/shared-layout"
import ConversionBackground from "@/components/conversion-background"
import { useEffect, useState } from "react"
import { Clock } from "lucide-react"
import Image from "next/image"
import { SkinRarityService } from "@/lib/api/skin-rarity-service"
import { useRouter } from "next/navigation"

interface BannerSkin {
  id: number
  name: string
  rarity: string
}

interface BannerData {
  id: string
  bannerBackgroundTexture: string
  bannerChaseAnimationWebmPath: string
  bannerChaseAnimationParallax: string
  bannerSkin: BannerSkin
  startDate: number
  endDate: number
  rollVignetteSkinIntroSfxPath?: string
  rollVignetteSkinIntroWebmPath?: string
}

interface BannerApiResponse {
  success: boolean
  data: BannerData[]
  timestamp: string
}

interface ProcessedSkinData {
  id: number
  name: string
  champion: string
  championKey: string
  tier: string
  rarity: string
  price: string
  image: string
  splashArt: string
  videoUrl?: string
  isLegacy: boolean
  isBase: boolean
  skinType: string
  contentId: string
  skinNumber: number
  skinLines: string[]
  description?: string
  featuresText?: string
  hasNewEffects: boolean | null
  hasNewAnimations: boolean | null
  hasNewRecall: boolean | null
  hasChromas: boolean
  set?: string
  releaseDate?: string
  availability: string | null
  lootEligible: boolean | null
  lastDiscount?: string
  itemId?: number
  questSkinInfo?: any
  newVoiceLines?: boolean | null
  isUpcoming?: boolean
  releaseCountdown?: string
}

interface SkinsApiResponse {
  success: boolean
  data: ProcessedSkinData[]
  timestamp: string
}

// Helper function to extract path after /ASSETS/ and convert to Community Dragon URL
const extractAssetPath = (fullPath: string): string => {
  const assetsIndex = fullPath.indexOf('/ASSETS/')
  if (assetsIndex === -1) return ''
  
  const pathAfterAssets = fullPath.substring(assetsIndex)
  return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${pathAfterAssets.toLowerCase()}`
}

// Helper function to convert skin name to display name
const formatSkinName = (skinName: string): string => {
  // Remove "Skin" prefix and numbers, then format
  const cleaned = skinName.replace(/Skin\d+$/, '').replace(/([A-Z])/g, ' $1').trim()
  return cleaned.charAt(0).toUpperCase() + cleaned.slice(1)
}

// Helper function to create URL-friendly skin slug (same as in /skins pages)
const createSkinSlug = (skinName: string): string => {
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

// Helper function to get rarity icon URL using SkinRarityService
const getRarityIconUrl = (rarity: string): string => {
  return SkinRarityService.getRarityIconUrl(rarity) || ''
}

// Helper function to format countdown
const formatCountdown = (endDate: number): string => {
  // Convert Unix timestamp (seconds) to milliseconds
  const end = new Date(endDate * 1000)
  const now = new Date()
  const diff = end.getTime() - now.getTime()

  if (diff <= 0) return 'Ended'

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) return `${days}d ${hours}h ${minutes}m`
  if (hours > 0) return `${hours}h ${minutes}m`
  return `${minutes}m`
}

export default function TheSanctumPage() {
  const router = useRouter()
  const [allBanners, setAllBanners] = useState<BannerData[]>([])
  const [selectedBanner, setSelectedBanner] = useState<BannerData | null>(null)
  const [skinData, setSkinData] = useState<ProcessedSkinData | null>(null)
  const [allSkinsData, setAllSkinsData] = useState<ProcessedSkinData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [countdown, setCountdown] = useState('')
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [previewVideoUrl, setPreviewVideoUrl] = useState<string>('')
  const [previewAudioUrl, setPreviewAudioUrl] = useState<string>('')
  const [isDropRatesMode, setIsDropRatesMode] = useState(false)
  const [dropRatesData, setDropRatesData] = useState<any>(null)
  const [enrichedDropRatesData, setEnrichedDropRatesData] = useState<any>(null)

  // Fetch banner data and skin data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch banner data
        const bannerResponse = await fetch(`http://localhost:3000/api/banners?t=${Date.now()}`)
        if (!bannerResponse.ok) {
          throw new Error(`HTTP ${bannerResponse.status}: Failed to fetch banner data`)
        }

        const bannerApiResponse: BannerApiResponse = await bannerResponse.json()

        if (!bannerApiResponse.success || !bannerApiResponse.data || bannerApiResponse.data.length === 0) {
          throw new Error('No banner data available')
        }

        // Store all banners and select the first one
        setAllBanners(bannerApiResponse.data)
        const currentBanner = bannerApiResponse.data[0]
        setSelectedBanner(currentBanner)

        // Fetch skin data using the banner skin ID from the same API as /skins page
        const skinResponse = await fetch(`/api/skins/all?t=${Date.now()}`)
        if (!skinResponse.ok) {
          throw new Error(`HTTP ${skinResponse.status}: Failed to fetch skin data`)
        }

        const skinApiResponse = await skinResponse.json()

        if (skinApiResponse.success && skinApiResponse.data) {
          // Store all skins data for banner sidebar
          setAllSkinsData(skinApiResponse.data)

          // Find the skin that matches the current banner skin ID
          const matchingSkin = skinApiResponse.data.find((skin: ProcessedSkinData) => skin.id === currentBanner.bannerSkin.id)
          if (matchingSkin) {
            setSkinData(matchingSkin)
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle banner selection
  const handleBannerSelect = async (banner: BannerData) => {
    setSelectedBanner(banner)

    // Fetch skin data for the selected banner
    try {
      const skinResponse = await fetch(`/api/skins/all?t=${Date.now()}`)
      if (skinResponse.ok) {
        const skinApiResponse = await skinResponse.json()
        if (skinApiResponse.success && skinApiResponse.data) {
          const matchingSkin = skinApiResponse.data.find((skin: ProcessedSkinData) => skin.id === banner.bannerSkin.id)
          setSkinData(matchingSkin || null)
        }
      }
    } catch (error) {
      console.error('Failed to fetch skin data for selected banner:', error)
    }
  }

  // Helper function to get skin info for a banner
  const getBannerSkinInfo = (banner: BannerData) => {
    const matchingSkin = allSkinsData.find(skin => skin.id === banner.bannerSkin.id)
    return {
      name: matchingSkin?.name || formatSkinName(banner.bannerSkin.name),
      rarity: matchingSkin?.tier || banner.bannerSkin.rarity
    }
  }

  // Handle preview button click
  const handlePreviewClick = () => {
    if (!selectedBanner) return

    // Extract and set preview URLs
    if (selectedBanner.rollVignetteSkinIntroWebmPath) {
      const videoUrl = extractAssetPath(selectedBanner.rollVignetteSkinIntroWebmPath)
      setPreviewVideoUrl(videoUrl)
    }

    if (selectedBanner.rollVignetteSkinIntroSfxPath) {
      const audioUrl = extractAssetPath(selectedBanner.rollVignetteSkinIntroSfxPath)
      setPreviewAudioUrl(audioUrl)
    }

    setIsPreviewMode(true)
  }

  // Handle skin page redirect
  const handleSkinPageRedirect = () => {
    if (!skinData) return

    const skinSlug = createSkinSlug(skinData.name)
    router.push(`/skins/${skinSlug}`)
  }

  // Handle drop rates button click
  const handleDropRatesClick = async () => {
    if (!selectedBanner) return

    try {
      // Fetch drop rates data
      const response = await fetch(`http://localhost:3000/api/banners/odds?t=${Date.now()}`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch drop rates data`)
      }

      const dropRatesResponse = await response.json()

      if (dropRatesResponse.success && dropRatesResponse.data) {
        // Find matching drop rates for current banner skin ID
        const currentSkinId = selectedBanner.bannerSkin.id

        // Check if any reward table children match the current skin ID
        const matchingDropRates = dropRatesResponse.data.rewardTables?.find((table: any) =>
          table.children?.some((child: any) => child.id === currentSkinId)
        )

        if (matchingDropRates) {
          // Enrich the data with skin/chroma images
          const enrichedData = await enrichDropRatesData(dropRatesResponse.data)
          setDropRatesData(dropRatesResponse.data)
          setEnrichedDropRatesData(enrichedData)
          setIsDropRatesMode(true)
        } else {
          console.warn('No matching drop rates found for current banner skin ID:', currentSkinId)
        }
      }
    } catch (error) {
      console.error('Failed to fetch drop rates data:', error)
    }
  }

  // Enrich drop rates data with skin/chroma images
  const enrichDropRatesData = async (dropRatesData: any) => {
    try {
      // Fetch all skins, chromas, summoner icons, and skin augments data
      const [skinsResponse, chromasResponse, summonerIconsResponse, skinAugmentsResponse] = await Promise.all([
        fetch('/api/skins/all'),
        fetch('/api/chromas/all'),
        fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/summoner-icons.json'),
        fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json')
      ])

      const skinsData = skinsResponse.ok ? await skinsResponse.json() : { data: [] }
      const chromasData = chromasResponse.ok ? await chromasResponse.json() : { data: [] }
      const summonerIconsData = summonerIconsResponse.ok ? await summonerIconsResponse.json() : []
      const skinAugmentsData = skinAugmentsResponse.ok ? await skinAugmentsResponse.json() : []

      const allSkins = skinsData.data || []
      const allChromas = chromasData.data || []

      // Build summoner icons map
      const summonerIconsMap = new Map()
      if (Array.isArray(summonerIconsData)) {
        summonerIconsData.forEach((icon: any) => {
          if (icon.id && icon.imagePath) {
            // Extract the path after "/lol-game-data/assets" and make it lowercase
            const pathMatch = icon.imagePath.match(/\/lol-game-data\/assets(.+)/)
            if (pathMatch) {
              const extractedPath = pathMatch[1].toLowerCase()
              summonerIconsMap.set(icon.id, {
                name: icon.title || `Summoner Icon ${icon.id}`,
                image: `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${extractedPath}`
              })
            }
          }
        })
      }

      // Build skin augments map
      const skinAugmentsMap = new Map()
      if (skinAugmentsData && typeof skinAugmentsData === 'object') {
        console.log('🔍 Processing skin augments data (object), total skins:', Object.keys(skinAugmentsData).length)

        // Check if the target skin exists
        const targetSkin = skinAugmentsData['25080']
        if (targetSkin) {
          console.log('🎯 Found target skin (Spirit Blossom Morgana):', {
            id: targetSkin.id,
            name: targetSkin.name,
            hasSkinAugments: !!targetSkin.skinAugments,
            skinAugments: targetSkin.skinAugments
          })
        }

        // Process each skin in the object
        Object.values(skinAugmentsData).forEach((skin: any) => {
          if (skin.skinAugments && skin.skinAugments.borders) {
            console.log('🔍 Processing skin with augments:', skin.name, skin.id)
            // Process borders in skinAugments
            Object.values(skin.skinAugments.borders).forEach((layerArray: any) => {
              if (Array.isArray(layerArray)) {
                layerArray.forEach((border: any) => {
                  if (border.contentId && border.borderPath) {
                    console.log('📝 Found skin augment border:', {
                      contentId: border.contentId,
                      borderPath: border.borderPath,
                      skinName: skin.name,
                      isTargetContentId: border.contentId === 'ccedf22a-be20-4eeb-97a5-20de37e2b82f'
                    })
                    // Extract path after /ASSETS/ and make it lowercase, but keep /assets/ in the URL
                    const pathMatch = border.borderPath.match(/\/ASSETS\/(.+)/)
                    if (pathMatch) {
                      const extractedPath = pathMatch[1].toLowerCase()
                      const finalUrl = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/${extractedPath}`
                      console.log('🎯 Skin augment URL generated:', {
                        contentId: border.contentId,
                        originalPath: border.borderPath,
                        extractedPath: extractedPath,
                        finalUrl: finalUrl
                      })
                      skinAugmentsMap.set(border.contentId, {
                        name: `${skin.name} Border` || `Skin Augment ${border.contentId}`,
                        image: finalUrl
                      })
                    }
                  }
                })
              }
            })
          }
        })
        console.log('✅ Total skin augments mapped:', skinAugmentsMap.size)
      } else {
        console.log('❌ skinAugmentsData is invalid:', typeof skinAugmentsData)
      }

      // Enrich each reward table
      const enrichedRewardTables = dropRatesData.rewardTables?.map((table: any) => ({
        ...table,
        children: table.children?.map((child: any) => {
          let enrichedChild = { ...child }

          if (child.type === 'CHAMPION_SKIN') {
            const matchingSkin = allSkins.find((skin: any) => skin.id === child.id)
            if (matchingSkin) {
              enrichedChild.skinData = {
                name: matchingSkin.name,
                image: matchingSkin.image,
                splashArt: matchingSkin.splashArt,
                champion: matchingSkin.champion,
                tier: matchingSkin.tier
              }
            }
          } else if (child.type === 'CHAMPION_SKIN_CHROMA') {
            const matchingChroma = allChromas.find((chroma: any) => chroma.id === child.id)
            if (matchingChroma) {
              enrichedChild.chromaData = {
                name: matchingChroma.name,
                image: matchingChroma.image,
                skinName: matchingChroma.skinName,
                color: matchingChroma.color,
                colors: matchingChroma.colors
              }
            }
          } else if (child.type === 'SUMMONER_ICON') {
            const matchingIcon = summonerIconsMap.get(child.id)
            if (matchingIcon) {
              enrichedChild.summonerIconData = {
                name: matchingIcon.name,
                image: matchingIcon.image
              }
            }
          } else if (child.type === 'SKIN_AUGMENT') {
            console.log('🔍 Looking for skin augment with itemInstanceId:', child.itemInstanceId)
            const matchingAugment = skinAugmentsMap.get(child.itemInstanceId)
            if (matchingAugment) {
              console.log('✅ Found matching skin augment:', matchingAugment)
              enrichedChild.skinAugmentData = {
                name: matchingAugment.name,
                image: matchingAugment.image
              }
            } else {
              console.log('❌ No matching skin augment found for itemInstanceId:', child.itemInstanceId)
            }
          }

          return enrichedChild
        }),
        fallbackChildren: table.fallbackChildren?.map((child: any) => {
          let enrichedChild = { ...child }

          if (child.type === 'CHAMPION_SKIN') {
            const matchingSkin = allSkins.find((skin: any) => skin.id === child.id)
            if (matchingSkin) {
              enrichedChild.skinData = {
                name: matchingSkin.name,
                image: matchingSkin.image,
                splashArt: matchingSkin.splashArt,
                champion: matchingSkin.champion,
                tier: matchingSkin.tier
              }
            }
          } else if (child.type === 'CHAMPION_SKIN_CHROMA') {
            const matchingChroma = allChromas.find((chroma: any) => chroma.id === child.id)
            if (matchingChroma) {
              enrichedChild.chromaData = {
                name: matchingChroma.name,
                image: matchingChroma.image,
                skinName: matchingChroma.skinName,
                color: matchingChroma.color,
                colors: matchingChroma.colors
              }
            }
          } else if (child.type === 'SUMMONER_ICON') {
            const matchingIcon = summonerIconsMap.get(child.id)
            if (matchingIcon) {
              enrichedChild.summonerIconData = {
                name: matchingIcon.name,
                image: matchingIcon.image
              }
            }
          } else if (child.type === 'SKIN_AUGMENT') {
            console.log('🔍 SKIN_AUGMENT ENRICHMENT:', child.itemInstanceId)
            const matchingAugment = skinAugmentsMap.get(child.itemInstanceId)
            if (matchingAugment) {
              console.log('✅ MATCH FOUND:', matchingAugment)
              enrichedChild.skinAugmentData = {
                name: matchingAugment.name,
                image: matchingAugment.image
              }
            } else {
              console.log('❌ NO MATCH for:', child.itemInstanceId)
            }
          }

          return enrichedChild
        })
      }))

      return {
        ...dropRatesData,
        rewardTables: enrichedRewardTables
      }
    } catch (error) {
      console.error('Failed to enrich drop rates data:', error)
      return dropRatesData
    }
  }

  // Handle video time update for fade effect
  const handleVideoTimeUpdate = (e: React.SyntheticEvent<HTMLVideoElement>) => {
    const video = e.currentTarget
    const duration = video.duration
    const currentTime = video.currentTime

    // Start fading out in the last 0.5 seconds
    if (duration - currentTime <= 0.5) {
      const fadeProgress = (duration - currentTime) / 0.5
      video.style.opacity = fadeProgress.toString()
    } else {
      video.style.opacity = '1'
    }
  }

  // Update countdown every minute
  useEffect(() => {
    if (!selectedBanner?.endDate) return

    const updateCountdown = () => {
      setCountdown(formatCountdown(selectedBanner.endDate))
    }

    updateCountdown()
    const interval = setInterval(updateCountdown, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [selectedBanner?.endDate])

  if (loading) {
    return (
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-400"></div>
          </div>
        </div>
      </SharedLayout>
    )
  }

  if (error || !selectedBanner) {
    return (
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="text-red-400 text-6xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Unable to Load Banner
              </h1>
              <p className="text-gray-300 mb-4">
                {error || 'No banner data available'}
              </p>
            </div>
          </div>
        </div>
      </SharedLayout>
    )
  }

  const backgroundVideoUrl = extractAssetPath(selectedBanner.bannerBackgroundTexture)
  const foregroundVideoUrl = extractAssetPath(selectedBanner.bannerChaseAnimationWebmPath)
  const skinDisplayName = skinData?.name || formatSkinName(selectedBanner.bannerSkin.name)
  const skinRarity = skinData?.tier || selectedBanner.bannerSkin.rarity
  const rarityIconUrl = skinRarity ? getRarityIconUrl(skinRarity) : ''

  return (
    <ConversionBackground customImageUrl="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/backgrounds/sanctum-background.jpg">
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-8 relative z-20">
          {/* Page Header */}
          <div className="mb-4 sm:mb-6 lg:mb-8">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2">The Sanctum</h1>
            <p className="text-sm sm:text-base text-gray-300">Current banners and exclusive content</p>
          </div>

          {/* Main Layout: Sidebar + Video Area */}
          <div className="flex flex-col lg:flex-row min-h-[300px] sm:min-h-[400px] lg:min-h-[480px]">
            {/* Left Sidebar - Banner Selection */}
            <div className="w-full lg:w-48 flex-shrink-0 mb-4 lg:mb-0">
              <div className="flex flex-row lg:flex-col space-x-4 lg:space-x-0 lg:space-y-4 overflow-x-auto lg:overflow-x-visible pb-2 lg:pb-0 pt-2 lg:pt-0">
                {allBanners.map((banner, index) => {
                  const skinInfo = getBannerSkinInfo(banner)
                  const matchingSkin = allSkinsData.find(skin => skin.id === banner.bannerSkin.id)
                  const skinImage = matchingSkin?.image || '/placeholder.svg'

                  return (
                    <button
                      key={banner.id || index}
                      onClick={() => handleBannerSelect(banner)}
                      className={`relative group transition-all duration-200 flex-shrink-0 ${
                        selectedBanner?.id === banner.id
                          ? 'scale-105'
                          : 'hover:scale-102'
                      }`}
                    >
                      {/* Frame Container */}
                      <div className="relative w-32 sm:w-36 lg:w-40 h-20 sm:h-22 lg:h-24 mx-auto overflow-visible">

                        {/* Skin Image */}
                        <div className="absolute inset-1 overflow-hidden">
                          <Image
                            src={skinImage}
                            alt={skinInfo.name}
                            fill
                            className="object-cover object-top"
                            onError={() => console.log('Image failed to load:', skinImage)}
                            onLoad={() => console.log('Image loaded successfully:', skinImage)}
                          />
                        </div>

                        {/* Frame Overlay */}
                        <div className="absolute inset-0 pointer-events-none z-10">
                          <Image
                            src="/frame.svg"
                            alt="Frame"
                            fill
                            className="object-contain"
                          />
                        </div>

                        {/* Skin Rarity Icon at Center Bottom on Frame Line */}
                        {skinInfo.rarity && getRarityIconUrl(skinInfo.rarity) && (
                          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 pointer-events-none z-20">
                            <Image
                              src={getRarityIconUrl(skinInfo.rarity)}
                              alt={`${skinInfo.rarity} rarity`}
                              width={16}
                              height={16}
                              className="w-4 h-4"
                            />
                          </div>
                        )}

                      </div>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Right Main Area - Video Display */}
            <div className="flex-1">
              <div className="bg-gray-900/60 border border-orange-700/30 rounded-lg overflow-hidden backdrop-blur-sm">
                {/* Video Container */}
                <div className="relative h-[300px] sm:h-[400px] md:h-[500px] lg:h-[680px] bg-black">
                  {/* Action Buttons - Mobile: bottom-right, Desktop: top-left */}
                  {!isPreviewMode && !isDropRatesMode && (
                    <div className="absolute bottom-4 right-4 md:bottom-auto md:right-auto md:top-4 md:left-4 z-30 flex flex-row gap-1">
                      {/* Preview Button */}
                      {selectedBanner?.rollVignetteSkinIntroWebmPath && (
                        <button
                          onClick={handlePreviewClick}
                          className="group transition-transform duration-200 hover:scale-105"
                        >
                          <div className="relative w-10 h-10">
                            {/* Button Background */}
                            <Image
                              src="/Button.svg"
                              alt="Preview Button Background"
                              fill
                              className="object-contain"
                            />
                            {/* Preview Icon Overlay - Centered */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <Image
                                src="/play-video.svg"
                                alt="Play Video Icon"
                                width={16}
                                height={16}
                                className="w-4 h-4"
                              />
                            </div>
                          </div>
                        </button>
                      )}

                      {/* Skin Page Button */}
                      {skinData && (
                        <button
                          onClick={handleSkinPageRedirect}
                          className="group transition-transform duration-200 hover:scale-105"
                        >
                          <div className="relative w-10 h-10">
                            {/* Button Background */}
                            <Image
                              src="/Button.svg"
                              alt="Skin Page Button Background"
                              fill
                              className="object-contain"
                            />
                            {/* Preview Icon Overlay - Centered */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <Image
                                src="/preview.svg"
                                alt="View Skin Page Icon"
                                width={18}
                                height={18}
                                className="w-[18px] h-[18px]"
                              />
                            </div>
                          </div>
                        </button>
                      )}

                      {/* Drop Rates Button */}
                      <button
                        onClick={handleDropRatesClick}
                        className="group transition-transform duration-200 hover:scale-105"
                      >
                        <div className="relative w-10 h-10">
                          {/* Button Background */}
                          <Image
                            src="/Button.svg"
                            alt="Drop Rates Button Background"
                            fill
                            className="object-contain"
                          />
                          {/* Ellipses Icon Overlay - Centered */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <Image
                              src="/ellipses.svg"
                              alt="Drop Rates Icon"
                              width={18}
                              height={18}
                              className="w-[18px] h-[18px]"
                            />
                          </div>
                        </div>
                      </button>
                    </div>
                  )}

                  {/* Preview Mode - Skin Intro Video */}
                  {isPreviewMode && previewVideoUrl ? (
                    <>
                      <video
                        src={previewVideoUrl}
                        autoPlay
                        muted={false}
                        playsInline
                        className="absolute inset-0 w-full h-full object-cover z-20 transition-opacity duration-300"
                        onTimeUpdate={handleVideoTimeUpdate}
                        onEnded={() => {
                          setIsPreviewMode(false)
                          setPreviewVideoUrl('')
                          setPreviewAudioUrl('')
                        }}
                        onError={() => {
                          console.error('❌ Preview video failed to load:', previewVideoUrl)
                        }}
                      />
                      {/* Preview Audio */}
                      {previewAudioUrl && (
                        <audio
                          src={previewAudioUrl}
                          autoPlay
                          onError={() => {
                            console.error('❌ Preview audio failed to load:', previewAudioUrl)
                          }}
                        />
                      )}
                    </>
                  ) : isDropRatesMode ? (
                    /* Drop Rates Mode - Full Video Container */
                    <div className="absolute inset-0 w-full h-full z-20 bg-black overflow-y-auto">
                      {/* Background Image */}
                      <div className="absolute inset-0">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/backgrounds/sanctum-background.jpg"
                          alt="Sanctum Background"
                          fill
                          className="object-cover opacity-30"
                        />
                      </div>

                      {/* Drop Rates Content */}
                      <div className="relative z-10 p-4 h-full overflow-y-auto">
                        <div className="max-w-full">
                          <div className="flex justify-between items-center mb-4">
                            <div></div>
                            <h3 className="text-xl font-bold text-white">Drop Rates</h3>
                            <button
                              onClick={() => {
                                setIsDropRatesMode(false)
                                setDropRatesData(null)
                                setEnrichedDropRatesData(null)
                              }}
                              className="text-gray-400 hover:text-white transition-colors text-xl"
                            >
                              ✕
                            </button>
                          </div>

                          {enrichedDropRatesData && (
                            <div className="space-y-4">
                              {/* Reward Tables */}
                              {enrichedDropRatesData.rewardTables?.map((table: any, tableIndex: number) => {
                                const tierNames = ['S Tier', 'A Tier', 'B Tier']
                                const tierName = tierNames[tableIndex] || `Tier ${tableIndex + 1}`

                                return (
                                  <div key={tableIndex} className="space-y-3">
                                    {/* Tier with rewards in flex layout */}
                                    <div className="flex gap-4">
                                      {/* Tier Label on the left */}
                                      <div className="flex-shrink-0 w-20 flex items-start justify-start pt-2">
                                        <h4 className="text-lg font-semibold text-orange-400 whitespace-nowrap">{tierName}</h4>
                                      </div>

                                      {/* Rewards on the right */}
                                      <div className="flex-1 space-y-3">
                                        {/* Main Children */}
                                        {table.children && table.children.length > 0 && (
                                          <div>
                                            <h5 className="text-sm font-medium text-white mb-2">Main Rewards</h5>
                                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                                        {(() => {
                                          // Check if this is an exalted skin (has CHAMPION_SKIN, SUMMONER_ICON, and SKIN_AUGMENT)
                                          const hasChampionSkin = table.children.some((child: any) => child.type === 'CHAMPION_SKIN')
                                          const summonerIcon = table.children.find((child: any) => child.type === 'SUMMONER_ICON')
                                          const skinAugment = table.children.find((child: any) => child.type === 'SKIN_AUGMENT')
                                          const isExaltedSkin = hasChampionSkin && summonerIcon && skinAugment

                                          if (isExaltedSkin) {
                                            // For exalted skins, combine summoner icon and border into one card
                                            const filteredChildren = table.children.filter((child: any) =>
                                              child.type !== 'SUMMONER_ICON' && child.type !== 'SKIN_AUGMENT'
                                            )

                                            // Add the combined icon+border card
                                            const combinedCard = {
                                              ...skinAugment,
                                              type: 'COMBINED_ICON_BORDER',
                                              odds: null, // Remove odds for combined card
                                              combinedSummonerIcon: summonerIcon,
                                              combinedSkinAugment: skinAugment
                                            }

                                            return [...filteredChildren, combinedCard].map((child: any, childIndex: number) => {
                                          // Only debug SKIN_AUGMENT items
                                          if (child.type === 'SKIN_AUGMENT') {
                                            console.log('🎨 RENDERING SKIN_AUGMENT:', {
                                              id: child.id,
                                              itemInstanceId: child.itemInstanceId,
                                              hasSkinAugmentData: !!child.skinAugmentData,
                                              imageUrl: child.skinAugmentData?.image
                                            })
                                          }

                                          // Check if this is Mythic Essence
                                          const isMythicEssence = child.itemInstanceId === "8ce03930-7079-5e3f-a49b-d4721b00dbb3"
                                          let mythicEssenceImage = null

                                          if (isMythicEssence) {
                                            if (child.quantity >= 200) {
                                              mythicEssenceImage = '/MythicEssence+200.png'
                                            } else {
                                              mythicEssenceImage = '/MythicEssence-200.png'
                                            }
                                          }

                                          return (
                                            <div key={childIndex} className="relative rounded-lg overflow-hidden bg-gray-700/50 aspect-square group">
                                              {/* Full-size Image Background */}
                                              {isMythicEssence && mythicEssenceImage ? (
                                                <Image
                                                  src={mythicEssenceImage}
                                                  alt="Mythic Essence"
                                                  fill
                                                  className="object-cover"
                                                  onError={(e) => {
                                                    e.currentTarget.src = '/placeholder.svg'
                                                  }}
                                                />
                                              ) : child.summonerIconData ? (
                                                <div className="absolute inset-0 flex items-center justify-center">
                                                  <div className="w-1/2 h-1/2 relative">
                                                    <Image
                                                      src={child.summonerIconData.image}
                                                      alt={child.summonerIconData.name}
                                                      fill
                                                      className="object-cover rounded-lg"
                                                      onError={(e) => {
                                                        e.currentTarget.src = '/placeholder.svg'
                                                      }}
                                                    />
                                                  </div>
                                                </div>
                                                  ) : child.type === 'COMBINED_ICON_BORDER' ? (
                                                <>
                                                  {/* Border as background */}
                                                  <Image
                                                    src={child.combinedSkinAugment?.skinAugmentData?.image || '/placeholder.svg'}
                                                    alt="Border"
                                                    fill
                                                    className="object-cover"
                                                    onError={(e) => {
                                                      e.currentTarget.src = '/placeholder.svg'
                                                    }}
                                                  />
                                                  {/* Summoner Icon centered on top */}
                                                  <div className="absolute inset-0 flex items-center justify-center">
                                                    <div className="w-1/3 h-1/3 relative">
                                                      <Image
                                                        src={child.combinedSummonerIcon?.summonerIconData?.image || '/placeholder.svg'}
                                                        alt="Summoner Icon"
                                                        fill
                                                        className="object-cover rounded-lg"
                                                        onError={(e) => {
                                                          e.currentTarget.src = '/placeholder.svg'
                                                        }}
                                                      />
                                                    </div>
                                                  </div>
                                                </>
                                              ) : (child.skinData || child.chromaData || child.skinAugmentData) && (
                                                <Image
                                                  src={child.skinData?.image || child.chromaData?.image || child.skinAugmentData?.image || '/placeholder.svg'}
                                                  alt={child.skinData?.name || child.chromaData?.name || child.skinAugmentData?.name || 'Item'}
                                                  fill
                                                  className="object-cover"
                                                  onError={(e) => {
                                                    if (child.skinAugmentData?.image) {
                                                      console.log('❌ SKIN_AUGMENT IMAGE FAILED TO LOAD:', child.skinAugmentData.image)
                                                    }
                                                    e.currentTarget.src = '/placeholder.svg'
                                                  }}
                                                />
                                              )}

                                              {/* Percentage Rectangle - Top Left */}
                                              <div className="absolute top-2 left-2 rounded px-1.5 py-0 z-10" style={{ backgroundColor: '#a09b8c' }}>
                                                <span className="text-black text-xs font-bold">
                                                  {child.odds.toFixed(3).replace(/\.?0+$/, '')}%
                                                </span>
                                              </div>

                                              {/* Text Overlay - Bottom */}
                                              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent p-3 z-10">
                                                <div className="text-center">
                                                  <div className="text-xs text-orange-400 font-medium mb-1">
                                                    {isMythicEssence ? 'Mythic Essence' : child.type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, (l: string) => l.toUpperCase())}
                                                  </div>

                                                  {isMythicEssence ? (
                                                    <div className="text-white text-sm font-bold">
                                                      {child.quantity > 0 ? `+${child.quantity}` : child.quantity} ME
                                                    </div>
                                                  ) : (
                                                    <>
                                                      {child.skinData && (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.skinData.name}
                                                        </div>
                                                      )}

                                                      {child.chromaData && (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.chromaData.name}
                                                        </div>
                                                      )}

                                                      {child.summonerIconData && (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.summonerIconData.name}
                                                        </div>
                                                      )}

                                                      {child.skinAugmentData && (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.skinAugmentData.name}
                                                        </div>
                                                      )}

                                                      {!child.skinData && !child.chromaData && !child.summonerIconData && !child.skinAugmentData && (
                                                        <div className="text-white text-sm font-bold">
                                                          {child.type === 'CURRENCY' ? `${child.quantity} ${child.type}` : `ID: ${child.id}`}
                                                        </div>
                                                      )}
                                                    </>
                                                  )}
                                                </div>
                                              </div>
                                            </div>
                                          )
                                          })
                                          } else {
                                            // Regular display for non-exalted skins
                                            return table.children.map((child: any, childIndex: number) => {
                                              // Only debug SKIN_AUGMENT items
                                              if (child.type === 'SKIN_AUGMENT') {
                                                console.log('🎨 RENDERING SKIN_AUGMENT:', {
                                                  id: child.id,
                                                  itemInstanceId: child.itemInstanceId,
                                                  hasSkinAugmentData: !!child.skinAugmentData,
                                                  imageUrl: child.skinAugmentData?.image
                                                })
                                              }

                                              // Check if this is Mythic Essence
                                              const isMythicEssence = child.itemInstanceId === "8ce03930-7079-5e3f-a49b-d4721b00dbb3"
                                              let mythicEssenceImage = null

                                              if (isMythicEssence) {
                                                if (child.quantity >= 200) {
                                                  mythicEssenceImage = '/MythicEssence+200.png'
                                                } else {
                                                  mythicEssenceImage = '/MythicEssence-200.png'
                                                }
                                              }

                                              return (
                                                <div key={childIndex} className="relative rounded-lg overflow-hidden bg-gray-700/50 aspect-square group">
                                                  {/* Full-size Image Background */}
                                                  {isMythicEssence && mythicEssenceImage ? (
                                                    <Image
                                                      src={mythicEssenceImage}
                                                      alt="Mythic Essence"
                                                      fill
                                                      className="object-cover"
                                                      onError={(e) => {
                                                        e.currentTarget.src = '/placeholder.svg'
                                                      }}
                                                    />
                                                  ) : child.summonerIconData ? (
                                                    <div className="absolute inset-0 flex items-center justify-center">
                                                      <div className="w-1/2 h-1/2 relative">
                                                        <Image
                                                          src={child.summonerIconData.image}
                                                          alt={child.summonerIconData.name}
                                                          fill
                                                          className="object-cover rounded-lg"
                                                          onError={(e) => {
                                                            e.currentTarget.src = '/placeholder.svg'
                                                          }}
                                                        />
                                                      </div>
                                                    </div>
                                                  ) : (child.skinData || child.chromaData || child.skinAugmentData) && (
                                                    <Image
                                                      src={child.skinData?.image || child.chromaData?.image || child.skinAugmentData?.image || '/placeholder.svg'}
                                                      alt={child.skinData?.name || child.chromaData?.name || child.skinAugmentData?.name || 'Item'}
                                                      fill
                                                      className="object-cover"
                                                      onError={(e) => {
                                                        if (child.skinAugmentData?.image) {
                                                          console.log('❌ SKIN_AUGMENT IMAGE FAILED TO LOAD:', child.skinAugmentData.image)
                                                        }
                                                        e.currentTarget.src = '/placeholder.svg'
                                                      }}
                                                    />
                                                  )}

                                                  {/* Percentage Rectangle - Top Left (only for items with odds) */}
                                                  {child.odds !== null && child.odds !== undefined && (
                                                    <div className="absolute top-2 left-2 rounded px-1.5 py-0 z-10" style={{ backgroundColor: '#a09b8c' }}>
                                                      <span className="text-black text-xs font-bold">
                                                        {child.odds.toFixed(3).replace(/\.?0+$/, '')}%
                                                      </span>
                                                    </div>
                                                  )}

                                                  {/* Text Overlay - Bottom */}
                                                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent p-3 z-10">
                                                    <div className="text-center">
                                                      <div className="text-xs text-orange-400 font-medium mb-1">
                                                        {child.type === 'COMBINED_ICON_BORDER' ? 'Icon & Border' :
                                                         isMythicEssence ? 'Mythic Essence' :
                                                         child.type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, (l: string) => l.toUpperCase())}
                                                      </div>

                                                      {child.type === 'COMBINED_ICON_BORDER' ? (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.combinedSkinAugment?.skinAugmentData?.name?.replace(' Border', '') || 'Icon & Border'}
                                                        </div>
                                                      ) : isMythicEssence ? (
                                                        <div className="text-white text-sm font-bold">
                                                          {child.quantity > 0 ? `+${child.quantity}` : child.quantity} ME
                                                        </div>
                                                      ) : (
                                                        <>
                                                          {child.skinData && (
                                                            <div className="text-white text-sm font-bold truncate">
                                                              {child.skinData.name}
                                                            </div>
                                                          )}

                                                          {child.chromaData && (
                                                            <div className="text-white text-sm font-bold truncate">
                                                              {child.chromaData.name}
                                                            </div>
                                                          )}

                                                          {child.summonerIconData && (
                                                            <div className="text-white text-sm font-bold truncate">
                                                              {child.summonerIconData.name}
                                                            </div>
                                                          )}

                                                          {child.skinAugmentData && (
                                                            <div className="text-white text-sm font-bold truncate">
                                                              {child.skinAugmentData.name}
                                                            </div>
                                                          )}

                                                          {!child.skinData && !child.chromaData && !child.summonerIconData && !child.skinAugmentData && (
                                                            <div className="text-white text-sm font-bold">
                                                              {child.type === 'CURRENCY' ? `${child.quantity} ${child.type}` : `ID: ${child.id}`}
                                                            </div>
                                                          )}
                                                        </>
                                                      )}
                                                    </div>
                                                  </div>
                                                </div>
                                              )
                                            })
                                          }
                                        })()}
                                            </div>
                                          </div>
                                        )}

                                        {/* Fallback Children */}
                                        {table.fallbackChildren && table.fallbackChildren.length > 0 && (
                                          <div>
                                            <h5 className="text-sm font-medium text-white mb-2">Fallback Rewards</h5>
                                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                                        {table.fallbackChildren.map((child: any, childIndex: number) => {
                                          // Check if this is Mythic Essence
                                          const isMythicEssence = child.itemInstanceId === "8ce03930-7079-5e3f-a49b-d4721b00dbb3"
                                          let mythicEssenceImage = null

                                          if (isMythicEssence) {
                                            if (child.quantity >= 200) {
                                              mythicEssenceImage = '/MythicEssence+200.png'
                                            } else {
                                              mythicEssenceImage = '/MythicEssence-200.png'
                                            }
                                          }

                                          return (
                                            <div key={childIndex} className="relative rounded-lg overflow-hidden bg-gray-700/50 aspect-square group">
                                              {/* Full-size Image Background */}
                                              {isMythicEssence && mythicEssenceImage ? (
                                                <Image
                                                  src={mythicEssenceImage}
                                                  alt="Mythic Essence"
                                                  fill
                                                  className="object-cover"
                                                  onError={(e) => {
                                                    e.currentTarget.src = '/placeholder.svg'
                                                  }}
                                                />
                                              ) : child.summonerIconData ? (
                                                <div className="absolute inset-0 flex items-center justify-center">
                                                  <div className="w-1/2 h-1/2 relative">
                                                    <Image
                                                      src={child.summonerIconData.image}
                                                      alt={child.summonerIconData.name}
                                                      fill
                                                      className="object-cover rounded-lg"
                                                      onError={(e) => {
                                                        e.currentTarget.src = '/placeholder.svg'
                                                      }}
                                                    />
                                                  </div>
                                                </div>
                                              ) : (child.skinData || child.chromaData || child.skinAugmentData) && (
                                                <Image
                                                  src={child.skinData?.image || child.chromaData?.image || child.skinAugmentData?.image || '/placeholder.svg'}
                                                  alt={child.skinData?.name || child.chromaData?.name || child.skinAugmentData?.name || 'Item'}
                                                  fill
                                                  className="object-cover"
                                                  onError={(e) => {
                                                    e.currentTarget.src = '/placeholder.svg'
                                                  }}
                                                />
                                              )}

                                              {/* Percentage Rectangle - Top Left */}
                                              <div className="absolute top-2 left-2 rounded px-1.5 py-0 z-10" style={{ backgroundColor: '#a09b8c' }}>
                                                <span className="text-black text-xs font-bold">
                                                  {child.odds.toFixed(3).replace(/\.?0+$/, '')}%
                                                </span>
                                              </div>

                                              {/* Text Overlay - Bottom */}
                                              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent p-3 z-10">
                                                <div className="text-center">
                                                  <div className="text-xs text-orange-400 font-medium mb-1">
                                                    {isMythicEssence ? 'Mythic Essence' : child.type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, (l: string) => l.toUpperCase())}
                                                  </div>

                                                  {isMythicEssence ? (
                                                    <div className="text-white text-sm font-bold">
                                                      {child.quantity > 0 ? `+${child.quantity}` : child.quantity} ME
                                                    </div>
                                                  ) : (
                                                    <>
                                                      {child.skinData && (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.skinData.name}
                                                        </div>
                                                      )}

                                                      {child.chromaData && (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.chromaData.name}
                                                        </div>
                                                      )}

                                                      {child.summonerIconData && (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.summonerIconData.name}
                                                        </div>
                                                      )}

                                                      {child.skinAugmentData && (
                                                        <div className="text-white text-sm font-bold truncate">
                                                          {child.skinAugmentData.name}
                                                        </div>
                                                      )}

                                                      {!child.skinData && !child.chromaData && !child.summonerIconData && !child.skinAugmentData && (
                                                        <div className="text-white text-sm font-bold">
                                                          {child.type === 'CURRENCY' ? `${child.quantity} ${child.type}` : `ID: ${child.id}`}
                                                        </div>
                                                      )}
                                                    </>
                                                  )}
                                                </div>
                                              </div>
                                            </div>
                                          )
                                        })}
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )
                              })}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <>
                      {/* Background Video */}
                      {backgroundVideoUrl && (
                        <video
                          src={backgroundVideoUrl}
                          autoPlay
                          loop
                          muted
                          playsInline
                          className="absolute inset-0 w-full h-full object-cover"
                          onError={() => {
                            console.error('❌ Background video failed to load:', backgroundVideoUrl)
                          }}
                        />
                      )}

                      {/* Foreground Video with Parallax */}
                      {foregroundVideoUrl && (
                        <video
                          src={foregroundVideoUrl}
                          autoPlay
                          loop
                          muted
                          playsInline
                          className="absolute inset-0 w-full h-full object-cover"
                          style={{
                            transform: `translateZ(${selectedBanner.bannerChaseAnimationParallax || 0}px)`
                          }}
                          onError={() => {
                            console.error('❌ Foreground video failed to load:', foregroundVideoUrl)
                          }}
                        />
                      )}
                    </>
                  )}

                  {/* Video Overlay with Banner Info */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
                    <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 lg:p-6">
                      <div className="flex items-end justify-between">
                        <div>
                          <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-1 sm:mb-2">{skinDisplayName}</h3>
                          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
                            {/* Countdown */}
                            <div className="flex items-center gap-2 text-orange-400">
                              <Clock size={14} className="sm:w-4 sm:h-4" />
                              <span className="text-xs sm:text-sm font-medium">{countdown}</span>
                            </div>

                            {/* Rarity with Icon */}
                            <div className="flex items-center gap-2">
                              {rarityIconUrl && (
                                <Image
                                  src={rarityIconUrl}
                                  alt={`${skinRarity} rarity`}
                                  width={16}
                                  height={16}
                                  className="w-4 h-4 sm:w-5 sm:h-5"
                                />
                              )}
                              <span className="text-xs sm:text-sm text-gray-300">{skinRarity}</span>
                            </div>
                          </div>


                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SharedLayout>
    </ConversionBackground>
  )
}
